"""
A.T.L.A.S. Real-Time Scanner - Compatibility Bridge
This file provides backward compatibility by importing from the consolidated scanner engine.
"""

import logging
import sys
import os

# Add consolidated path to imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'atlas_v5_consolidated', 'market'))

# Import the consolidated scanner implementation
try:
    from atlas_scanner_engine import (
        AtlasRealtimeScanner,
        ScannerConfig,
        ScannerStatus,
        ScanResult,
        PatternAlert
    )
    
    logger = logging.getLogger(__name__)
    logger.info("[BRIDGE] Successfully imported AtlasRealtimeScanner from consolidated engine")
    
except ImportError as e:
    logger = logging.getLogger(__name__)
    logger.error(f"[BRIDGE] Failed to import from consolidated scanner: {e}")
    
    # Fallback implementation for critical functionality
    class AtlasRealtimeScanner:
        """Fallback implementation of AtlasRealtimeScanner"""
        
        def __init__(self):
            self.logger = logging.getLogger(__name__)
            self.is_running = False
            self.scan_count = 0
            self.active_signals = {}
            self.logger.warning("[FALLBACK] Using fallback AtlasRealtimeScanner implementation")
        
        async def initialize(self):
            """Initialize the scanner"""
            self.logger.info("[FALLBACK] Scanner initialized in fallback mode")
            return True
        
        async def start_scanner(self):
            """Start the scanner"""
            self.is_running = True
            self.logger.info("[FALLBACK] Scanner started in fallback mode")
            return True
        
        async def stop_scanner(self):
            """Stop the scanner"""
            self.is_running = False
            self.logger.info("[FALLBACK] Scanner stopped")
            return True
        
        async def get_active_signals(self):
            """Get active signals"""
            return []
        
        async def get_scanner_status(self):
            """Get scanner status"""
            return {
                "status": "running" if self.is_running else "stopped",
                "scan_count": self.scan_count,
                "active_signals": len(self.active_signals),
                "mode": "fallback"
            }

# Export the main class for compatibility
__all__ = ['AtlasRealtimeScanner', 'ScannerConfig', 'ScannerStatus', 'ScanResult', 'PatternAlert']
