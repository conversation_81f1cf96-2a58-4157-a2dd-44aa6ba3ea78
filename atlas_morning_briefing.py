#!/usr/bin/env python3
"""
A.T.L.A.S. Morning Market Briefing System
Generates comprehensive market snapshots at market open and on-demand via chat
"""

import asyncio
import logging
import pytz
from datetime import datetime, time as dt_time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import json

# Import existing A.T.L.A.S. components
from atlas_lee_method import LeeMethodScanner, LeeMethodSignal
from atlas_realtime_scanner import AtlasRealtimeScanner

logger = logging.getLogger(__name__)

@dataclass
class TradeSetup:
    """Trade setup data structure"""
    symbol: str
    setup_type: str
    confidence: float
    entry_price: float
    target_price: float
    stop_loss: float
    risk_reward_ratio: float
    description: str
    stars: int  # 1-5 star rating
    
@dataclass
class SectorAnalysis:
    """Sector analysis data structure"""
    sector: str
    symbol: str  # ETF symbol
    performance: float
    status: str  # "Strong", "Weak", "Mixed"
    recommendation: str

@dataclass
class MarketBriefing:
    """Complete market briefing data structure"""
    timestamp: datetime
    market_status: str
    major_indexes: Dict[str, Dict[str, Any]]
    top_setups: List[TradeSetup]
    sector_analysis: List[SectorAnalysis]
    notable_news: List[str]
    beginner_tip: str
    trading_mode: str
    vix_level: float
    market_sentiment: str

class AtlasMorningBriefing:
    """A.T.L.A.S. Morning Briefing Generator"""
    
    def __init__(self):
        self.scanner = LeeMethodScanner()
        self.realtime_scanner = AtlasRealtimeScanner()
        self.last_briefing: Optional[MarketBriefing] = None
        self.briefing_generated_today = False
        
        # Market open time (9:30 AM ET)
        self.market_open_time = dt_time(9, 30)
        
        # Beginner tips pool
        self.beginner_tips = [
            "A clean setup means clear risk. If your stop and target are fuzzy—pass.",
            "Use 3:1 reward-to-risk setups. If you're risking $100, look for at least $300 potential profit.",
            "Volume confirms price action. High volume breakouts are more reliable than low volume ones.",
            "Never risk more than 2% of your account on a single trade.",
            "The best trades often feel uncomfortable at first. Trust your analysis over emotions.",
            "Markets trend 30% of the time and range 70%. Adjust your strategy accordingly.",
            "Support becomes resistance, and resistance becomes support once broken.",
            "When in doubt, stay out. There's always another opportunity tomorrow."
        ]
        
        logger.info("✅ A.T.L.A.S. Morning Briefing System initialized")
    
    async def generate_morning_briefing(self) -> MarketBriefing:
        """Generate comprehensive morning market briefing"""
        logger.info("📊 Generating morning market briefing...")
        
        try:
            # Check if market is open
            market_status = self.scanner.get_market_status()
            
            # Generate major indexes analysis
            major_indexes = await self._analyze_major_indexes()
            
            # Get top trade setups using Lee Method
            top_setups = await self._get_top_trade_setups()
            
            # Analyze sectors
            sector_analysis = await self._analyze_sectors()
            
            # Get notable news (placeholder - would integrate with news API)
            notable_news = await self._get_notable_news()
            
            # Get beginner tip
            beginner_tip = self._get_daily_beginner_tip()
            
            # Determine trading mode
            trading_mode = self._determine_trading_mode(major_indexes, top_setups)
            
            # Calculate VIX and sentiment
            vix_level = await self._get_vix_level()
            market_sentiment = self._analyze_market_sentiment(vix_level, major_indexes)
            
            briefing = MarketBriefing(
                timestamp=datetime.now(),
                market_status=market_status['market_status'],
                major_indexes=major_indexes,
                top_setups=top_setups,
                sector_analysis=sector_analysis,
                notable_news=notable_news,
                beginner_tip=beginner_tip,
                trading_mode=trading_mode,
                vix_level=vix_level,
                market_sentiment=market_sentiment
            )
            
            self.last_briefing = briefing
            self.briefing_generated_today = True
            
            logger.info("✅ Morning briefing generated successfully")
            return briefing
            
        except Exception as e:
            logger.error(f"❌ Error generating morning briefing: {e}")
            raise
    
    async def _analyze_major_indexes(self) -> Dict[str, Dict[str, Any]]:
        """Analyze major market indexes"""
        # In production, this would fetch real data from APIs
        # For now, using realistic mock data structure
        
        indexes = {
            "S&P 500": {
                "symbol": "SPY",
                "price": 5265.50,
                "change": -0.23,
                "description": "Minor pullback after 5-day rally"
            },
            "NASDAQ": {
                "symbol": "QQQ", 
                "price": 18080.2,
                "change": -0.11,
                "description": "Tech digesting gains"
            },
            "Dow Jones": {
                "symbol": "DIA",
                "price": 40150.8,
                "change": -0.15,
                "description": "Consolidating near highs"
            }
        }
        
        return indexes
    
    async def _get_top_trade_setups(self) -> List[TradeSetup]:
        """Get top trade setups using Lee Method scanner"""
        try:
            # High-probability symbols for scanning
            symbols = ['TSLA', 'AAPL', 'NVDA', 'MSFT', 'GOOGL', 'META', 'AMZN', 'AMD', 'CRM', 'NFLX']
            
            # Scan for signals
            signals = await self.scanner.scan_multiple_symbols(symbols)
            
            # Convert signals to trade setups
            setups = []
            for signal in signals:
                if signal and signal.confidence >= 0.75:  # High confidence only
                    setup = TradeSetup(
                        symbol=signal.symbol,
                        setup_type=signal.pattern_type,
                        confidence=signal.confidence,
                        entry_price=signal.entry_price,
                        target_price=signal.target_price,
                        stop_loss=signal.stop_loss,
                        risk_reward_ratio=signal.risk_reward_ratio,
                        description=signal.description,
                        stars=self._calculate_stars(signal.confidence)
                    )
                    setups.append(setup)
            
            # Sort by confidence and return top 3
            setups.sort(key=lambda x: x.confidence, reverse=True)
            return setups[:3]
            
        except Exception as e:
            logger.error(f"Error getting trade setups: {e}")
            # Return mock setups for demonstration
            return self._get_mock_trade_setups()
    
    def _get_mock_trade_setups(self) -> List[TradeSetup]:
        """Mock trade setups for demonstration"""
        return [
            TradeSetup(
                symbol="TSLA",
                setup_type="Bullish continuation after volume spike",
                confidence=0.84,
                entry_price=275.00,
                target_price=290.00,
                stop_loss=266.50,
                risk_reward_ratio=1.76,
                description="Earnings beat + volume spike + trend continuation",
                stars=4
            ),
            TradeSetup(
                symbol="AAPL", 
                setup_type="Squeeze fired + RSI rebound",
                confidence=0.82,
                entry_price=193.50,
                target_price=198.80,
                stop_loss=189.00,
                risk_reward_ratio=1.18,
                description="Strong support at $192 + momentum improving",
                stars=4
            ),
            TradeSetup(
                symbol="NVDA",
                setup_type="50-SMA bounce + options flow bullish", 
                confidence=0.79,
                entry_price=135.50,
                target_price=140.00,
                stop_loss=131.90,
                risk_reward_ratio=1.25,
                description="Earnings in 3 weeks - IV buildup potential",
                stars=4
            )
        ]
    
    async def _analyze_sectors(self) -> List[SectorAnalysis]:
        """Analyze sector performance"""
        # Mock sector data - in production would fetch real ETF data
        sectors = [
            SectorAnalysis("Energy", "XLE", -1.4, "Weak", "Avoid today"),
            SectorAnalysis("Consumer", "XLY", 0.8, "Strong", "Rotation into retail"),
            SectorAnalysis("Technology", "XLK", 0.2, "Mixed", "AI/Tech still trending up"),
            SectorAnalysis("Healthcare", "XLV", -0.3, "Weak", "Defensive rotation out"),
            SectorAnalysis("Financials", "XLF", 0.5, "Strong", "Rate environment supportive")
        ]
        
        return sectors
    
    async def _get_notable_news(self) -> List[str]:
        """Get notable market news"""
        # Mock news - in production would integrate with news APIs
        return [
            "Fed minutes drop tomorrow — possible rate clarity",
            "China stimulus lifting materials sector slightly", 
            "Apple hints at AI announcement next month",
            "Earnings season 70% complete - beat rate at 68%",
            "Oil inventory data shows unexpected draw"
        ]
    
    def _get_daily_beginner_tip(self) -> str:
        """Get daily beginner tip"""
        # Rotate through tips based on day of year
        day_index = datetime.now().timetuple().tm_yday % len(self.beginner_tips)
        return self.beginner_tips[day_index]
    
    def _determine_trading_mode(self, indexes: Dict, setups: List[TradeSetup]) -> str:
        """Determine recommended trading mode"""
        avg_change = sum(idx['change'] for idx in indexes.values()) / len(indexes)
        setup_count = len(setups)
        
        if avg_change > 0.5 and setup_count >= 3:
            return "Active Trading - Strong momentum"
        elif avg_change < -0.5:
            return "Defensive - Wait for clarity"
        elif setup_count >= 2:
            return "Selective - Quality setups available"
        else:
            return "Watchlist & Light Action - Look for confirmation"
    
    async def _get_vix_level(self) -> float:
        """Get VIX level"""
        # Mock VIX data - in production would fetch real data
        return 14.6
    
    def _analyze_market_sentiment(self, vix: float, indexes: Dict) -> str:
        """Analyze overall market sentiment"""
        avg_change = sum(idx['change'] for idx in indexes.values()) / len(indexes)
        
        if vix < 15 and avg_change > 0:
            return "Bullish - Low fear, positive momentum"
        elif vix < 15 and avg_change < 0:
            return "Neutral - Low fear but minor pullback"
        elif vix > 25:
            return "Bearish - High fear environment"
        else:
            return "Mixed - Moderate volatility"
    
    def _calculate_stars(self, confidence: float) -> int:
        """Calculate star rating from confidence"""
        if confidence >= 0.85:
            return 5
        elif confidence >= 0.80:
            return 4
        elif confidence >= 0.75:
            return 3
        elif confidence >= 0.70:
            return 2
        else:
            return 1
    
    def format_briefing_for_chat(self, briefing: MarketBriefing) -> str:
        """Format briefing for chat interface display"""
        
        # Get current time in ET
        et_tz = pytz.timezone('US/Eastern')
        current_time = datetime.now(et_tz)
        
        output = f"""🌅 **MARKET SNAPSHOT – {current_time.strftime('%B %d, %Y')} (Morning Briefing)**
A.T.L.A.S v5.0 // Beginner Mode // Trade Ideas & Market Overview

📊 **Major Indexes**
"""
        
        # Add indexes
        for name, data in briefing.major_indexes.items():
            change_emoji = "📈" if data['change'] > 0 else "📉" if data['change'] < 0 else "➡️"
            output += f"{change_emoji} **{name}**: {data['price']:,.2f} ({data['change']:+.2f}%) – {data['description']}\n"
        
        output += f"\n**VIX**: {briefing.vix_level} – {briefing.market_sentiment}\n"
        
        # Add top setups
        if briefing.top_setups:
            output += "\n📈 **Top Trade Setups (Lee Method – Strong Signals)**\n\n"
            
            for i, setup in enumerate(briefing.top_setups, 1):
                stars = "⭐" * setup.stars
                output += f"**{i}️⃣ {setup.symbol}** – {stars}\n"
                output += f"• **Setup**: {setup.setup_type}\n"
                output += f"• **Entry**: ${setup.entry_price:.2f} • **Target**: ${setup.target_price:.2f} • **Stop**: ${setup.stop_loss:.2f}\n"
                output += f"• **{setup.description}**\n"
                output += f"• **Confidence**: {setup.confidence:.0%}\n\n"
        
        # Add sector analysis
        output += "📊 **Sector Focus**\n\n"
        for sector in briefing.sector_analysis:
            emoji = "🔋" if sector.sector == "Energy" else "🛍️" if sector.sector == "Consumer" else "🧠" if sector.sector == "Technology" else "🏥" if sector.sector == "Healthcare" else "🏦"
            status_emoji = "📈" if sector.status == "Strong" else "📉" if sector.status == "Weak" else "➡️"
            output += f"{emoji} **{sector.sector}**: {sector.status} ({sector.symbol} {sector.performance:+.1f}%) – {sector.recommendation}\n"
        
        # Add notable news
        if briefing.notable_news:
            output += "\n📡 **Notable News**\n\n"
            for news in briefing.notable_news:
                output += f"• {news}\n"
        
        # Add beginner tip
        output += f"\n📘 **Beginner Tip of the Day**\n🧠 \"{briefing.beginner_tip}\"\n"
        
        # Add trading mode
        output += f"\n📈 **Today's Mode**: {briefing.trading_mode}\n"
        output += "✔️ Look for confirmation before entry\n"
        
        return output
    
    async def should_generate_briefing(self) -> bool:
        """Check if briefing should be generated"""
        market_status = self.scanner.get_market_status()
        
        # Generate if market is open and we haven't generated today
        if market_status['market_open'] and not self.briefing_generated_today:
            return True
        
        # Reset flag at market close
        if not market_status['market_open']:
            self.briefing_generated_today = False
        
        return False
    
    async def get_briefing_for_chat(self) -> str:
        """Get briefing formatted for chat interface"""
        try:
            # Check if we need to generate a new briefing
            if await self.should_generate_briefing() or self.last_briefing is None:
                briefing = await self.generate_morning_briefing()
            else:
                briefing = self.last_briefing
            
            return self.format_briefing_for_chat(briefing)
            
        except Exception as e:
            logger.error(f"Error getting briefing for chat: {e}")
            return "❌ Unable to generate market briefing at this time. Please try again."

# Global instance
morning_briefing = AtlasMorningBriefing()

# Export main components
__all__ = ['AtlasMorningBriefing', 'MarketBriefing', 'TradeSetup', 'morning_briefing']
